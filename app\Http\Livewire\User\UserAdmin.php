<?php

namespace App\Http\Livewire\User;

use App\Mail\WelcomeUserMail;
use App\Models\State;
use App\Models\User;
use App\Services\LogService;
use Illuminate\Support\Facades\Mail;
use Livewire\Component;
use Illuminate\Support\Str;

class UserAdmin extends Component
{

    public User $user;
    public $states = [];

    public function mount(User $user)
    {
        $this->user = $user;
        $this->loadStates();
    }

    public function loadStates()
    {
        $this->states = State::orderBy('name')->get();
    }

    public function rules()
    {
        return [
            'user.first_name' => 'required',
            'user.email' => $this->user->id ? 'required|email|unique:users,email,' . $this->user->id : 'required|email|unique:users,email',
            'user.last_name' => 'required',
            'user.role' => 'required',
            'user.is_active' => 'boolean|nullable',
        ];
    }

    public function update($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $this->validate();

        if (!$this->user->id) {
            $randomPassword = Str::random(8);
            $this->user->password = bcrypt($randomPassword);
            $this->user->password_changed_at = null; // Set to null to force password change on first login

            // Save the Admin to the database
            $this->user->save();

            // Log staff creation
            LogService::logStaffCreated($this->user);

            // Send welcome email with credentials
            try {
                Mail::to($this->user->email)
                    ->send(new WelcomeUserMail($this->user, $randomPassword));

                // Log temporary password sent
                LogService::logTempPasswordSent($this->user);

                $message = "Provider created successfully. Login credentials have been sent to {$this->user->email}";
            } catch (\Throwable $e) {
                $message = "User created successfully, but the notification email could not be sent. Please provide the temporary password separately: " . "password";
            }
        } else {
            // Get original values before saving for change tracking
            $originalValues = LogService::getModelAttributes($this->user->getOriginal(), [
                'first_name', 'last_name', 'email', 'phone', 'role', 'is_active'
            ]);

            // Save the user to the database for updates
            $this->user->save();

            // Get new values after saving
            $newValues = LogService::getModelAttributes($this->user, [
                'first_name', 'last_name', 'email', 'phone', 'role', 'is_active'
            ]);

            // Log staff editing with changes
            LogService::logStaffEdited($this->user, $originalValues, $newValues);

            $message = " Info updated successfully.";
        }

        session()->flash('success-message', $message);

        return redirect()->route('admin.index');
    }

    public function render()
    {
        return view('livewire.user.user-admin');
    }
}
