@extends('master')

@php
    use App\Models\User;
    use App\Models\SystemLog;
@endphp

@section('content')
    <div class="card card-custom mb-5">
        <div class="card-body">

            <div class="row justify-content-between">
                <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-6">
                    <div class="input-icon">
                        <input type="text" class="form-control" placeholder="Search logs..." id="logs_search" />
                        <span>
                            <i class="flaticon2-search-1 text-muted"></i>
                        </span>
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <label for="type_filter">Type:</label>
                    <select class="form-control" id="type_filter">
                        <option value="">All Types</option>
                    </select>
                </div>

                <div class="col-md-3 mb-3">
                    <label for="user_type_filter">User Type:</label>
                    <select class="form-control" id="user_type_filter">
                        <option value="">All User Types</option>
                    </select>
                </div>

                <div class="col-md-3 mb-3">
                    <label for="date_from_filter">Date From:</label>
                    <input type="date" class="form-control" id="date_from_filter" max="{{ date('Y-m-d') }}" />
                </div>

                <div class="col-md-3 mb-3">
                    <label for="date_to_filter">Date To:</label>
                    <div class="input-group">
                        <input type="date" class="form-control" id="date_to_filter" max="{{ date('Y-m-d') }}" />
                        <div class="input-group-append">
                            <button class="btn btn-secondary" type="button" id="clear_filters">
                                <i class="fa fa-times"></i> Clear
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="datatable datatable-bordered datatable-head-custom" id="logs_dt"></div>

        </div>
    </div>

    <!-- Log Details Modal -->
    <div class="modal fade" id="logDetailsModal" tabindex="-1" role="dialog" aria-labelledby="logDetailsModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="logDetailsModalLabel">Log Details</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <i class="ki ki-close"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="log-details-content">
                        <!-- Log details will be loaded here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('styles')
    <style>
        .text-primary {
            text-decoration: none;
        }

        .text-primary:hover {
            text-decoration: underline;
        }

        /* Custom styles for the log details modal */
        #logDetailsModal .modal-dialog {
            max-width: 90%;
        }

        /* Simple style for clickable rows */
        #logs_dt tbody tr td:not(:last-child) {
            cursor: pointer;
        }

        /* Highlight on hover */
        #logs_dt tbody tr:hover td:not(:last-child) {
            background-color: rgba(54, 153, 255, 0.1) !important;
        }

        .log-context {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .badge-type {
            font-size: 11px;
            padding: 4px 8px;
        }

        .badge-system-error {
            background-color: #f841538f;
        }

        .badge-user-action {
            background-color: #007bff9a;
        }

        .badge-login {
            background-color: #28a746ad;
        }

        .badge-logout {
            background-color: #6c757dad;
        }

        .badge-script {
            background-color: #17a3b8a9;
        }

        .badge-provider {
            background-color: #fd7d14b7;
        }

        .badge-staff {
            background-color: #6e42c1b2;
        }

        .badge-password {
            background-color: #e83e8db9;
        }

        .badge-settings {
            background-color: #20c996af;
        }

        .changes-table {
            margin-top: 15px;
        }

        .changes-table table {
            font-size: 13px;
        }

        .changes-table .old-value {
            background-color: #f8d7da;
            color: #721c24;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }

        .changes-table .new-value {
            background-color: #d4edda;
            color: #155724;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }

        .changes-table .field-name {
            font-weight: bold;
            text-transform: capitalize;
        }

        .no-changes {
            font-style: italic;
            color: #6c757d;
        }
    </style>
@endsection

@section('scripts')
    <script>
        var datatable;
        var datatableElement;
        var searchElement;
        var columnArray;

        const apiRoute = `{{ route('settings.logs.api') }}`;
        const typesRoute = `{{ route('settings.logs.types') }}`;
        const userTypesRoute = `{{ route('settings.logs.user-types') }}`;

        datatableElement = $('#logs_dt');
        searchElement = $('#logs_search');

        // Load filter options
        loadFilterOptions();

        function loadFilterOptions() {
            // Load log types
            fetch(typesRoute)
                .then(response => response.json())
                .then(data => {
                    const typeSelect = $('#type_filter');
                    Object.entries(data).forEach(([key, value]) => {
                        typeSelect.append(`<option value="${key}">${value}</option>`);
                    });
                });

            // Load user types
            fetch(userTypesRoute)
                .then(response => response.json())
                .then(data => {
                    const userTypeSelect = $('#user_type_filter');
                    Object.entries(data).forEach(([key, value]) => {
                        userTypeSelect.append(`<option value="${key}">${value}</option>`);
                    });
                });
        }

        function getBadgeClass(type) {
            if (type.includes('error')) return 'badge-system-error';
            if (type.includes('login') || type.includes('logout')) return type.includes('login') ? 'badge-login' :
                'badge-logout';
            if (type.includes('script')) return 'badge-script';
            if (type.includes('provider')) return 'badge-provider';
            if (type.includes('staff')) return 'badge-staff';
            if (type.includes('password')) return 'badge-password';
            if (type.includes('settings')) return 'badge-settings';
            return 'badge-user-action';
        }

        columnArray = [{
                field: 'timestamp',
                title: 'Timestamp',
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    return data.formatted_timestamp;
                }
            },
            {
                field: 'type',
                title: 'Type',
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    const badgeClass = getBadgeClass(data.type);
                    return `<span class="badge ${badgeClass} badge-type">
                        ${data.type_label}
                    </span>`;
                }
            },
            {
                field: 'user_type',
                title: 'User Type',
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    return data.user_type || '-';
                }
            },
            {
                field: 'username',
                title: 'Username',
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    return data.username || '-';
                }
            },
            {
                field: 'message',
                title: 'Message',
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    const maxLength = 100;
                    const message = data.message || '';
                    if (message.length > maxLength) {
                        return `<div style="white-space: normal; text-wrap: wrap;">${message.substring(0, maxLength)}...</div>`;
                    }
                    return `<div style="white-space: normal; text-wrap: wrap;">${message}</div>`;
                }
            },
            {
                field: 'Actions',
                title: 'Actions',
                width: 'auto',
                sortable: false,
                overflow: 'visible',
                autoHide: false,
                template: function(data) {
                    return `<button class="btn btn-sm btn-clean btn-icon view-details-btn"
                               data-id="${data.id}" title="View Details">
                            <i class="menu-icon fas fa-eye"></i>
                        </button>`;
                }
            }
        ];

        datatable = datatableElement.KTDatatable({
            data: {
                type: 'remote',
                source: {
                    read: {
                        url: apiRoute,
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        params: function() {
                            const query = datatable.getDataSourceQuery();
                            const searchValue = $(searchElement).val() || '';
                            const typeFilter = $('#type_filter').val() || '';
                            const userTypeFilter = $('#user_type_filter').val() || '';
                            const dateFromFilter = $('#date_from_filter').val() || '';
                            const dateToFilter = $('#date_to_filter').val() || '';

                            const params = {
                                type: typeFilter,
                                user_type: userTypeFilter,
                                date_from: dateFromFilter,
                                date_to: dateToFilter,
                                search: searchValue,
                                query: {
                                    type: typeFilter,
                                    user_type: userTypeFilter,
                                    date_from: dateFromFilter,
                                    date_to: dateToFilter,
                                    search: searchValue
                                }
                            };

                            // Add any additional query parameters from datatable
                            if (query && query.query) {
                                Object.assign(params.query, query.query);
                            }

                            return params;
                        },
                        map: function(raw) {
                            var dataSet = raw;
                            if (typeof raw.data !== 'undefined') {
                                dataSet = raw.data;
                            }
                            return dataSet;
                        }
                    },
                },
                pageSize: 25,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            pagination: true,
            search: {
                input: searchElement,
                key: 'search',
                delay: 500
            },
            layout: {
                customScrollbar: false,
                scroll: true,
            },
            columns: columnArray
        });

        // Initialize the datatable query parameters
        datatable.setDataSourceQuery({
            query: {
                type: '',
                user_type: '',
                date_from: '',
                date_to: '',
                search: ''
            }
        });

        // Fix pagination dropdown issue - reinitialize selectpicker after datatable updates
        datatable.on('datatable-on-layout-updated', function() {
            setTimeout(function() {
                // Reinitialize selectpicker for pagination dropdown
                $('.datatable-pager-size.selectpicker').selectpicker('refresh');
            }, 100);
        });

        // Also reinitialize after data reload
        datatable.on('datatable-on-reloaded', function() {
            setTimeout(function() {
                // Reinitialize selectpicker for pagination dropdown
                $('.datatable-pager-size.selectpicker').selectpicker('refresh');
            }, 100);
        });

        // Add event handlers for filter elements
        $('#type_filter, #user_type_filter, #date_from_filter, #date_to_filter').on('change', function() {
            // Get current filter values
            const typeFilter = $('#type_filter').val();
            const userTypeFilter = $('#user_type_filter').val();
            const dateFromFilter = $('#date_from_filter').val();
            const dateToFilter = $('#date_to_filter').val();
            const searchValue = $(searchElement).val() || '';

            // Set the query parameters for the datatable
            datatable.setDataSourceQuery({
                type: typeFilter,
                user_type: userTypeFilter,
                date_from: dateFromFilter,
                date_to: dateToFilter,
                search: searchValue,
                query: {
                    type: typeFilter,
                    user_type: userTypeFilter,
                    date_from: dateFromFilter,
                    date_to: dateToFilter,
                    search: searchValue
                }
            });

            // Reload the datatable with the new query parameters
            datatable.reload();
        });

        // Clear filters
        $('#clear_filters').on('click', function() {
            $('#type_filter').val('');
            $('#user_type_filter').val('');
            $('#date_from_filter').val('');
            $('#date_to_filter').val('');

            // Reset the datatable query parameters
            datatable.setDataSourceQuery({
                type: '',
                user_type: '',
                date_from: '',
                date_to: '',
                search: $(searchElement).val() || '',
                query: {
                    type: '',
                    user_type: '',
                    date_from: '',
                    date_to: '',
                    search: $(searchElement).val() || ''
                }
            });

            datatable.reload();
        });

        // Handle row clicks for details
        $(document).on('click', '#logs_dt tbody tr td:not(:last-child)', function() {
            const row = $(this).closest('tr');
            const data = datatable.row(row).data();
            showLogDetails(data);
        });

        // Handle view details button clicks
        $(document).on('click', '.view-details-btn', function(e) {
            e.stopPropagation();
            const id = $(this).data('id');

            // Show loading modal immediately
            $('#logDetailsModal').modal('show');

            // Get the row data from the datatable first
            const row = $(this).closest('tr');
            let data = null;

            // Try to get data from KTDatatable
            try {
                data = datatable.row(row).data();
            } catch (error) {
                console.log('Could not get data from datatable:', error);
            }

            // If we have data from datatable and it has the correct ID, use it
            if (data && data.id && data.id == id) {
                showLogDetails(data);
            } else {
                const apiUrl = new URL(`{{ route('settings.logs.api') }}`);
                apiUrl.searchParams.append('log_id', id);

                fetch(apiUrl, {
                        method: 'GET',
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}',
                            'Accept': 'application/json',
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(result => {
                        if (result.data && result.data.length > 0) {
                            showLogDetails(result.data[0]);
                        } else if (result.id) {
                            // If the result is a single object, not an array
                            showLogDetails(result);
                        } else {
                            $('#log-details-content').html(
                                '<div class="alert alert-warning">Could not load log details - no data found</div>'
                                );
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching log details:', error);
                        $('#log-details-content').html(
                            `<div class="alert alert-danger">Error loading log details: ${error.message}</div>`
                            );
                    });
            }
        });

        function showLogDetails(data) {

            // Validate that we have the required data
            if (!data || !data.id) {
                $('#log-details-content').html('<div class="alert alert-warning">Invalid log data received</div>');
                return;
            }

            let contextHtml = '';
            let changesHtml = '';

            if (data.context && Object.keys(data.context).length > 0) {
                // Check if context contains changes
                if (data.context.changes && Object.keys(data.context.changes).length > 0) {
                    changesHtml = formatChangesHtml(data.context.changes);
                }

                // Show other context data (excluding changes)
                const otherContext = Object.fromEntries(
                    Object.entries(data.context).filter(([key]) => key !== 'changes')
                );

                if (Object.keys(otherContext).length > 0) {
                    contextHtml = `
                    <h6>Context:</h6>
                    <div class="log-context">${Object.entries(otherContext).map(([key, value]) =>
                        `<div><strong>${key}:</strong> ${typeof value === 'object' ? JSON.stringify(value) : value}</div>`
                    ).join('')}</div>
                `;
                }
            }

            const detailsHtml = `
            <div class="row">
                <div class="col-md-6">
                    <strong>Timestamp:</strong><br>
                    ${data.formatted_timestamp || moment(data.timestamp).format('MMMM DD, YYYY HH:mm:ss')}
                </div>
                <div class="col-md-6">
                    <strong>Type:</strong><br>
                    <span class="badge ${getBadgeClass(data.type)} badge-type">${data.type_label || data.type}</span>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-md-6">
                    <strong>User Type:</strong><br>
                    ${data.user_type_label || data.user_type || '-'}
                </div>
                <div class="col-md-6">
                    <strong>Username:</strong><br>
                    ${data.username || '-'}
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-md-6">
                    <strong>IP Address:</strong><br>
                    ${data.ip_address || '-'}
                </div>
                <div class="col-md-6">
                    <strong>User Agent:</strong><br>
                    ${data.user_agent ? data.user_agent.substring(0, 50) + '...' : '-'}
                </div>
            </div>
            <hr>
            <h6>Message:</h6>
            <p>${data.message || 'No message'}</p>
            ${changesHtml}
            ${contextHtml}
        `;

            $('#log-details-content').html(detailsHtml);
        }

        function formatChangesHtml(changes) {
            if (!changes || Object.keys(changes).length === 0) {
                return '';
            }

            let changesTable = `
                <div class="changes-table">
                    <h6>Changes Made:</h6>
                    <table class="table table-sm table-bordered">
                        <thead>
                            <tr>
                                <th style="width: 25%;">Field</th>
                                <th style="width: 37.5%;">Old Value</th>
                                <th style="width: 37.5%;">New Value</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            Object.entries(changes).forEach(([field, change]) => {
                const fieldName = formatFieldName(field);
                const oldValue = change.old !== null && change.old !== undefined ? change.old : '<em>Empty</em>';
                const newValue = change.new !== null && change.new !== undefined ? change.new : '<em>Empty</em>';

                changesTable += `
                    <tr>
                        <td class="field-name">${fieldName}</td>
                        <td><span class="old-value">${oldValue}</span></td>
                        <td><span class="new-value">${newValue}</span></td>
                    </tr>
                `;
            });

            changesTable += `
                        </tbody>
                    </table>
                </div>
            `;

            return changesTable;
        }

        function formatFieldName(fieldName) {
            // Convert snake_case to Title Case
            return fieldName.split('_').map(word =>
                word.charAt(0).toUpperCase() + word.slice(1)
            ).join(' ');
        }
    </script>
@endsection
